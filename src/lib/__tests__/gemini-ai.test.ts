import { categorizeWithRules, categorizeWithAI } from '../gemini-ai';

// Mock the GoogleGenerativeAI class and its methods
jest.mock('@google/generative-ai', () => {
  const mockGenerateContent = jest.fn();
  const mockGetGenerativeModel = jest.fn().mockReturnValue({
    generateContent: mockGenerateContent,
  });

  return {
    GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
      getGenerativeModel: mockGetGenerativeModel,
    })),
    HarmCategory: {
      HARM_CATEGORY_HARASSMENT: 'HARM_CATEGORY_HARASSMENT',
      HARM_CATEGORY_HATE_SPEECH: 'HARM_CATEGORY_HATE_SPEECH',
      HARM_CATEGORY_SEXUALLY_EXPLICIT: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      HARM_CATEGORY_DANGEROUS_CONTENT: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    },
    HarmBlockThreshold: {
      BLOCK_MEDIUM_AND_ABOVE: 'BLOCK_MEDIUM_AND_ABOVE',
    },
  };
});

// Mock environment variables
const originalEnv = process.env;

describe('Transaction Categorization', () => {
  // Rule-based categorization tests
  describe('categorizeWithRules', () => {
    test('should categorize salary-related transactions as Income', () => {
      expect(categorizeWithRules('Monthly Salary')).toBe('Income');
      expect(categorizeWithRules('PAYROLL DEPOSIT')).toBe('Income');
      expect(categorizeWithRules('Direct Deposit - Employer')).toBe('Income');
    });

    test('should categorize grocery-related transactions as Groceries', () => {
      expect(categorizeWithRules('WALMART GROCERY')).toBe('Groceries');
      expect(categorizeWithRules('Whole Foods Market')).toBe('Groceries');
      expect(categorizeWithRules('KROGER FOOD')).toBe('Groceries');
    });

    test('should categorize restaurant-related transactions as Dining Out', () => {
      expect(categorizeWithRules('McDonalds Restaurant')).toBe('Dining Out');
      expect(categorizeWithRules('STARBUCKS CAFE')).toBe('Dining Out');
      expect(categorizeWithRules('Local Bar and Grill')).toBe('Dining Out');
    });

    test('should categorize unknown transactions as Other', () => {
      expect(categorizeWithRules('MISC PAYMENT')).toBe('Other');
      expect(categorizeWithRules('Unknown Vendor')).toBe('Other');
    });
  });

  // AI-based categorization tests
  describe('categorizeWithAI', () => {
    beforeEach(() => {
      // Reset mocks and environment before each test
      jest.clearAllMocks();
      process.env = { ...originalEnv };
      process.env.NEXT_PUBLIC_GEMINI_API_KEY = 'test-api-key';
    });

    afterAll(() => {
      // Restore original environment
      process.env = originalEnv;
    });

    test('should return AI categorization when successful', async () => {
      // Mock the AI response
      const mockResponse = {
        response: {
          text: jest.fn().mockReturnValue('Groceries'),
        },
      };
      
      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockGenerateContent = GoogleGenerativeAI().getGenerativeModel().generateContent;
      mockGenerateContent.mockResolvedValue(mockResponse);

      const result = await categorizeWithAI('TRADER JOES');
      expect(result).toBe('Groceries');
      expect(mockGenerateContent).toHaveBeenCalled();
    });

    test('should handle AI responses that are not in the category list', async () => {
      // Mock the AI response with a category not in our list
      const mockResponse = {
        response: {
          text: jest.fn().mockReturnValue('Food and Beverages'),
        },
      };
      
      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockGenerateContent = GoogleGenerativeAI().getGenerativeModel().generateContent;
      mockGenerateContent.mockResolvedValue(mockResponse);

      const result = await categorizeWithAI('TRADER JOES');
      expect(result).toBe('Other'); // Should default to 'Other' when category not found
      expect(mockGenerateContent).toHaveBeenCalled();
    });

    test('should fallback to Other when API key is missing', async () => {
      // Remove API key
      delete process.env.NEXT_PUBLIC_GEMINI_API_KEY;
      
      const result = await categorizeWithAI('TRADER JOES');
      expect(result).toBe('Other');
      
      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      expect(GoogleGenerativeAI).not.toHaveBeenCalled();
    });

    test('should handle API errors gracefully', async () => {
      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockGenerateContent = GoogleGenerativeAI().getGenerativeModel().generateContent;
      mockGenerateContent.mockRejectedValue(new Error('API Error'));

      const result = await categorizeWithAI('TRADER JOES');
      expect(result).toBe('Other');
      expect(mockGenerateContent).toHaveBeenCalled();
    });
  });
});
